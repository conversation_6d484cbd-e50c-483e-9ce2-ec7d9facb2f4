import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'dart:convert';

import 'package:gp_stock_app/generated/json/usdt_wallet.g.dart';
export 'package:gp_stock_app/generated/json/usdt_wallet.g.dart';

@JsonSerializable()
class USDTWalletList {
  List<USDTWallet> list = [];

  USDTWalletList();

  factory USDTWalletList.fromJson(Map<String, dynamic> json) => $USDTWalletListFromJson(json);

  Map<String, dynamic> toJson() => $USDTWalletListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class USDTWallet {
  int id = 0; // 主键ID
  int isWithdrawDefault = 0; // 是否默认提现地址 (0=否, 1=是)
  int network = 0; // 网络 (1=TRC20, 2=ERC20, 3=BEP20 等)
  @JSONField(name: "netWorkName")
  String networkName = ''; // 网络名称 TRC20 ERC20
  int userId = 0; // 用户ID
  String walletAddress = ''; // 钱包地址

  USDTWallet();

  factory USDTWallet.fromJson(Map<String, dynamic> json) => $USDTWalletFromJson(json);

  Map<String, dynamic> toJson() => $USDTWalletToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}


@JsonSerializable()
class USDTNetworkTypeList {
  List<USDTNetworkType> list = [];

  USDTNetworkTypeList();

  factory USDTNetworkTypeList.fromJson(Map<String, dynamic> json) => $USDTNetworkTypeListFromJson(json);

  Map<String, dynamic> toJson() => $USDTNetworkTypeListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class USDTNetworkType {
  int code = 0; // id
  String name = ''; // 名称


  USDTNetworkType();

  factory USDTNetworkType.fromJson(Map<String, dynamic> json) => $USDTNetworkTypeFromJson(json);

  Map<String, dynamic> toJson() => $USDTNetworkTypeToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
