import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_wallet.dart';
import 'package:gp_stock_app/core/services/http/http.dart';

class WalletApi {
  /// 获取所有usdt钱包地址
  static Future<List<USDTWallet>> fetchUsdtWalletAddressList() async {
    final res = await Http().request<USDTWalletList>(
      ApiEndpoints.getUsdtWalletAddressList,
      method: HttpMethod.get,
    );
    return res.data?.list ?? [];
  }
  /// 获取usdt网络类型
  static Future<List<USDTNetworkType>> fetchUsdtNetworkTypeList() async {
    final res = await Http().request<USDTNetworkTypeList>(
      ApiEndpoints.getUsdtNetworkType,
      method: HttpMethod.get,
    );
    return res.data?.list ?? [];
  }

  /// 新增usdt钱包地址
  static Future<bool> addUsdtWalletAddress({
    required String walletAddress, // 钱包地址
    required int network, // 网络 (1=TRC20, 2=ERC20, 3=BEP20 等)
    required bool isWithdrawDefault, // 是否默认提现地址 (0=否, 1=是)
  }) async {
    final res = await Http().request(
      ApiEndpoints.addUsdtWalletAddress,
      method: HttpMethod.post,
      params: {
        "walletAddress": walletAddress,
        "network": network,
        "isWithdrawDefault": isWithdrawDefault ? 1 : 0,
      },
    );
    return res.isSuccess;
  }

  /// 编辑usdt钱包地址
  static Future<bool> editUsdtWalletAddress({
    required int id, // 唯一标识
    required String walletAddress, // 钱包地址
    required int network, // 网络 (1=TRC20, 2=ERC20, 3=BEP20 等)
    required bool isWithdrawDefault, // 是否默认提现地址 (0=否, 1=是)
  }) async {
    final res = await Http().request(
      ApiEndpoints.editUsdtWalletAddress,
      method: HttpMethod.post,
      params: {
        "id": id,
        "walletAddress": walletAddress,
        "network": network,
        "isWithdrawDefault": isWithdrawDefault ? 1 : 0,
      },
    );
    return res.isSuccess;
  }

  /// 删除usdt钱包地址
  static Future<bool> deleteUsdtWalletAddress({
    required int id, // 唯一标识
  }) async {
    final res = await Http().request(
      "${ApiEndpoints.deleteUsdtWalletAddress}/$id",
      method: HttpMethod.delete,
      // queryParameters: {"id": id},
    );
    return res.isSuccess;
  }

  /// USDT提现
  static Future<bool> withdrawUSDT({
    required double amount, // 提现金额
    required int id, // 钱包地址ID
    required String password, // 资金密码
    required int walletId, // 提现通道ID
  }) async {
    final res = await Http().request<bool>(
      ApiEndpoints.withdrawUSDT,
      method: HttpMethod.post,
      params: {
        "amount": amount,
        "id": id,
        "password": password,
        "walletId": walletId,
      },
    );
    return res.isSuccess;
  }
}
