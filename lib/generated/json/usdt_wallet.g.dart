import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_wallet.dart';

USDTWalletList $USDTWalletListFromJson(Map<String, dynamic> json) {
  final USDTWalletList uSDTWalletList = USDTWalletList();
  final List<USDTWallet>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<USDTWallet>(e) as USDTWallet).toList();
  if (list != null) {
    uSDTWalletList.list = list;
  }
  return uSDTWalletList;
}

Map<String, dynamic> $USDTWalletListToJson(USDTWalletList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension USDTWalletListExtension on USDTWalletList {
  USDTWalletList copyWith({
    List<USDTWallet>? list,
  }) {
    return USDTWalletList()
      ..list = list ?? this.list;
  }
}

USDTWallet $USDTWalletFromJson(Map<String, dynamic> json) {
  final USDTWallet uSDTWallet = USDTWallet();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    uSDTWallet.id = id;
  }
  final int? isWithdrawDefault = jsonConvert.convert<int>(json['isWithdrawDefault']);
  if (isWithdrawDefault != null) {
    uSDTWallet.isWithdrawDefault = isWithdrawDefault;
  }
  final int? network = jsonConvert.convert<int>(json['network']);
  if (network != null) {
    uSDTWallet.network = network;
  }
  final String? networkName = jsonConvert.convert<String>(json['netWorkName']);
  if (networkName != null) {
    uSDTWallet.networkName = networkName;
  }
  final int? userId = jsonConvert.convert<int>(json['userId']);
  if (userId != null) {
    uSDTWallet.userId = userId;
  }
  final String? walletAddress = jsonConvert.convert<String>(json['walletAddress']);
  if (walletAddress != null) {
    uSDTWallet.walletAddress = walletAddress;
  }
  return uSDTWallet;
}

Map<String, dynamic> $USDTWalletToJson(USDTWallet entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['isWithdrawDefault'] = entity.isWithdrawDefault;
  data['network'] = entity.network;
  data['netWorkName'] = entity.networkName;
  data['userId'] = entity.userId;
  data['walletAddress'] = entity.walletAddress;
  return data;
}

extension USDTWalletExtension on USDTWallet {
  USDTWallet copyWith({
    int? id,
    int? isWithdrawDefault,
    int? network,
    String? networkName,
    int? userId,
    String? walletAddress,
  }) {
    return USDTWallet()
      ..id = id ?? this.id
      ..isWithdrawDefault = isWithdrawDefault ?? this.isWithdrawDefault
      ..network = network ?? this.network
      ..networkName = networkName ?? this.networkName
      ..userId = userId ?? this.userId
      ..walletAddress = walletAddress ?? this.walletAddress;
  }
}

USDTNetworkTypeList $USDTNetworkTypeListFromJson(Map<String, dynamic> json) {
  final USDTNetworkTypeList uSDTNetworkTypeList = USDTNetworkTypeList();
  final List<USDTNetworkType>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<USDTNetworkType>(e) as USDTNetworkType).toList();
  if (list != null) {
    uSDTNetworkTypeList.list = list;
  }
  return uSDTNetworkTypeList;
}

Map<String, dynamic> $USDTNetworkTypeListToJson(USDTNetworkTypeList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension USDTNetworkTypeListExtension on USDTNetworkTypeList {
  USDTNetworkTypeList copyWith({
    List<USDTNetworkType>? list,
  }) {
    return USDTNetworkTypeList()
      ..list = list ?? this.list;
  }
}

USDTNetworkType $USDTNetworkTypeFromJson(Map<String, dynamic> json) {
  final USDTNetworkType uSDTNetworkType = USDTNetworkType();
  final int? code = jsonConvert.convert<int>(json['code']);
  if (code != null) {
    uSDTNetworkType.code = code;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    uSDTNetworkType.name = name;
  }
  return uSDTNetworkType;
}

Map<String, dynamic> $USDTNetworkTypeToJson(USDTNetworkType entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  data['name'] = entity.name;
  return data;
}

extension USDTNetworkTypeExtension on USDTNetworkType {
  USDTNetworkType copyWith({
    int? code,
    String? name,
  }) {
    return USDTNetworkType()
      ..code = code ?? this.code
      ..name = name ?? this.name;
  }
}