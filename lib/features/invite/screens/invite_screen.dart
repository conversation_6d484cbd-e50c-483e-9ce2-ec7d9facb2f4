import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/icon_helper.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/invite/logic/invite/invite_cubit.dart';
import 'package:gp_stock_app/features/invite/logic/invite/invite_state.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';

import '../../../core/api/network/endpoint/urls.dart';
import '../../../shared/constants/assets.dart';
import '../../../shared/widgets/error/error_retry_widget.dart';
import '../widgets/invite_shimmer_widget.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'subordinate_list_screen.dart';
import 'rebate_details_screen.dart';

class InviteScreen extends StatefulWidget {
  final AppSkinStyle? skinStyle;

  const InviteScreen({super.key, this.skinStyle});

  @override
  State<InviteScreen> createState() => _InviteScreenState();
}

class _InviteScreenState extends State<InviteScreen> {
  @override
  void initState() {
    super.initState();
    context.read<InviteCubit>().getInviteDetails();
  }

  @override
  Widget build(BuildContext context) {
    final currentSkinStyle = widget.skinStyle ?? AppConfig.instance.skinStyle;

    return Scaffold(
      appBar: _buildAppBar(context, currentSkinStyle),
      body: BlocBuilder<InviteCubit, InviteState>(
        builder: (context, state) {
          if (state.status.isLoading) {
            return const InviteScreenShimmer();
          }

          if (state.status.isFailed) {
            return ErrorRetryWidget(
              errorMessage: state.error,
              onRetry: () => context.read<InviteCubit>().getInviteDetails(),
            );
          }

          return _buildBody(context, state, currentSkinStyle);
        },
      ),
    );
  }

  /// Build app bar based on skin style
  PreferredSizeWidget _buildAppBar(BuildContext context, AppSkinStyle skinStyle) {
    switch (skinStyle) {
      case AppSkinStyle.kGP:
        return AppBar(
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: context.colorTheme.textTitle,
              size: 20.gw,
            ),
            onPressed: () => Navigator.pop(context),
          ),
          title: Text('inviteAndEarn'.tr(), style: context.textTheme.title.fs18),
          backgroundColor: context.colorTheme.inviteBackgroundEnd,
          elevation: 0,
        );
      case AppSkinStyle.kTemplateA:
      case AppSkinStyle.kTemplateB:
      case AppSkinStyle.kTemplateC:
      case AppSkinStyle.kTemplateD:
        return AppBar(
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: context.colorTheme.textTitle,
              size: 20.gw,
            ),
            onPressed: () => Navigator.pop(context),
          ),
          title: Text('inviteAndEarn'.tr(), style: context.textTheme.primary.fs18.w600.copyWith(color: Colors.white)),
          backgroundColor: context.colorTheme.inviteBackgroundEnd,
          elevation: 0,
          foregroundColor: Colors.white,
        );
    }
  }

  /// Build body
  Widget _buildBody(BuildContext context, InviteState state, AppSkinStyle skinStyle) {
    return Container(
      decoration: BoxDecoration(
        color: context.colorTheme.inviteBackgroundEnd,
      ),
      child: Column(
        children: [
          // Action buttons
          Container(
            margin: EdgeInsets.only(top: 10.gh, left: 10.gw, right: 10.gw, bottom: 0.gh),
            decoration: BoxDecoration(
              color: context.colorTheme.inviteBackgroundStart,
              borderRadius: BorderRadius.vertical(top: Radius.circular(26)),
            ),
            padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 16.gh),
            child: Column(
              children: [
                _buildActionButtons(context, state),
                // Stats cards section
                _buildStatsCards(context, state),
              ],
            ),
          ),

          // Main content area
          Expanded(
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.white,
              ),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    // VIP level table
                    _buildVipLevelTable(context, state),
                    SizedBox(height: 20.gh),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build stats cards section
  Widget _buildStatsCards(BuildContext context, InviteState state) {
    return Container(
      padding: EdgeInsets.only(top: 8.gh, bottom: 8.gh, left: 10.gw),
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(Assets.inviteStatCardBg),
          fit: BoxFit.cover,
        ),
        borderRadius: BorderRadius.circular(16.gr),
      ),
      child: Column(
        children: [
          // VIP level indicator
          _buildVipLevelIndicator(context, state),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: EdgeInsets.fromLTRB(16.gr, 16.gr, 8.gr, 0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'cumulativeRebate'.tr(),
                            style: context.textTheme.primary.fs12.copyWith(color: const Color(0xFFC44500)),
                          ),
                          SizedBox(height: 10.gh),
                          Text(
                            state.inviteDetail?.totalRebate?.toStringAsFixed(2) ?? '0.00',
                            style: context.textTheme.primary.fs18.w500.copyWith(color: const Color(0xFF1D2129)),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.fromLTRB(16.gr, 16.gr, 8.gr, 0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'tradingRebateRate'.tr(),
                            style: context.textTheme.primary.fs12.copyWith(color: const Color(0xFFC44500)),
                          ),
                          SizedBox(height: 10.gh),
                          Text(
                            '${state.inviteDetail?.tradingRebateRate?.toStringAsFixed(0) ?? '0'}%',
                            style: context.textTheme.primary.fs18.w500.copyWith(color: const Color(0xFF1D2129)),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: EdgeInsets.fromLTRB(16.gr, 16.gr, 8.gr, 0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'cumulativeSubordinates'.tr(),
                            style: context.textTheme.primary.fs12.copyWith(color: const Color(0xFFC44500)),
                          ),
                          SizedBox(height: 10.gh),
                          Text(
                            '${state.inviteDetail?.totalSubordinates ?? 0}（${'effective'.tr()}：${state.inviteDetail?.totalSatisfiedInviteCount ?? 0}）',
                            style: context.textTheme.primary.fs18.w500.copyWith(color: const Color(0xFF1D2129)),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.fromLTRB(16.gr, 16.gr, 8.gr, 0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'interestRebateRate'.tr(),
                            style: context.textTheme.primary.fs12.copyWith(color: const Color(0xFFC44500)),
                          ),
                          SizedBox(height: 10.gh),
                          Text(
                            '${state.inviteDetail?.interestRebateRate?.toStringAsFixed(0) ?? '0'}%',
                            style: context.textTheme.primary.fs18.w500.copyWith(color: const Color(0xFF1D2129)),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build VIP level indicator
  Widget _buildVipLevelIndicator(BuildContext context, InviteState state) {
    return Container(
      child: Text(
        'VIP${state.inviteDetail?.currentVipLevel ?? 0}',
        style: context.textTheme.primary.fs16.copyWith(color: const Color(0xFFC44500)),
      ),
    );
  }

  /// Build VIP level table
  Widget _buildVipLevelTable(BuildContext context, InviteState state) {
    return Container(
      margin: EdgeInsets.all(12.gr),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'agentLevelDescription'.tr(),
            style: context.textTheme.primary.fs16.copyWith(color: const Color(0xFF222222)),
          ),
          SizedBox(height: 16.gh),
          // Table header
          Container(
            padding: EdgeInsets.symmetric(vertical: 8.gh),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    'agentLevel'.tr(),
                    style: context.textTheme.primary.copyWith(fontSize: 14.gsp, color: const Color(0xFF8897B8)),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    'rebateConditions'.tr(),
                    style: context.textTheme.primary.copyWith(fontSize: 14.gsp, color: const Color(0xFF8897B8)),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'rebateRatio'.tr(),
                    style: context.textTheme.primary.copyWith(fontSize: 14.gsp, color: const Color(0xFF8897B8)),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'interestRatio'.tr(),
                    style: context.textTheme.primary.copyWith(fontSize: 14.gsp, color: const Color(0xFF8897B8)),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
          // Table rows
          ...?state.inviteDetail?.vipLevelTableList?.map((vipLevel) =>
              _buildVipLevelRow(context, vipLevel, state.inviteDetail?.currentVipLevel == vipLevel.level)),
        ],
      ),
    );
  }

  /// Build individual VIP level row
  Widget _buildVipLevelRow(BuildContext context, dynamic vipLevel, bool isCurrentLevel) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.gh),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: const Color(0xFFDEDEDE), width: 1),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              'VIP${vipLevel.level ?? 0}',
              style: context.textTheme.primary.fs16.copyWith(color: const Color(0xFF222222)),
            ),
          ),
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  '${vipLevel.requiredInviteCount ?? 0} ${'validMembers'.tr()}(${'current'.tr()}:${vipLevel.satisfiedInviteCount ?? 0}/${vipLevel.requiredInviteCount ?? 0})',
                  style: context.textTheme.primary.copyWith(fontSize: 11.gsp, color: const Color(0xFF222222)),
                  textAlign: TextAlign.center,
                ),
                Text(
                  '${'cumulativeRecharge'.tr()} ${vipLevel.requiredRechargeAmount?.toStringAsFixed(0) ?? '0'}/${vipLevel.requiredInviteRechargeAmount?.toStringAsFixed(0) ?? '0'}',
                  style: context.textTheme.primary.copyWith(fontSize: 11.gsp, color: const Color(0xFF222222)),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '${vipLevel.rebateRate?.toStringAsFixed(1) ?? '0.0'}%',
              style: context.textTheme.primary.copyWith(fontSize: 14.gsp, color: const Color(0xFF222222)),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '${vipLevel.interestRate?.toStringAsFixed(1) ?? '0.0'}%',
              style: context.textTheme.primary.copyWith(fontSize: 14.gsp, color: const Color(0xFF222222)),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  /// Build action buttons section
  Widget _buildActionButtons(BuildContext context, InviteState state) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 20.gh),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildActionButton(
            context,
            icon: Assets.usersIcon,
            label: 'downloadList'.tr(),
            onTap: () => _navigateToSubordinateList(context),
          ),
          _buildActionButton(
            context,
            icon: Assets.rebateIcon,
            label: 'rebateDetails'.tr(),
            onTap: () => _navigateToRebateDetails(context),
          ),
          _buildActionButton(
            context,
            icon: Assets.inviteIcon,
            label: 'generateLink'.tr(),
            onTap: () => _generateInviteLink(context, state),
          ),
        ],
      ),
    );
  }

  /// Build individual action button
  Widget _buildActionButton(
    BuildContext context, {
    required String icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 30.gw,
            height: 30.gh,
            child: IconHelper.loadAsset(
              icon,
              width: 24.gw,
              height: 24.gh,
            ),
          ),
          SizedBox(height: 8.gh),
          Text(
            label,
            style: context.textTheme.primary.copyWith(fontSize: 14.gsp, color: const Color(0xFFFEEAAB)),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Navigate to subordinate list screen
  void _navigateToSubordinateList(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SubordinateListScreen(
          skinStyle: widget.skinStyle,
        ),
      ),
    );
  }

  /// Navigate to rebate details screen
  void _navigateToRebateDetails(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RebateDetailsScreen(
          skinStyle: widget.skinStyle,
        ),
      ),
    );
  }

  /// Generate invite link
  void _generateInviteLink(BuildContext context, InviteState state) {
    final inviteCode = state.inviteDetail?.inviteCode ?? '';
    final inviteLink = state.customInviteLink ?? '${Urls.inviteLink}$inviteCode';

    Clipboard.setData(ClipboardData(text: inviteLink));
    Helper.showFlutterToast('inviteLinkCopiedToClipboard'.tr());
  }
}
