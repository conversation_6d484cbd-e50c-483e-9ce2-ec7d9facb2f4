import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gp_stock_app/core/models/apis/wallet.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';

import 'usdt_wallet_manage_state.dart';

class UsdtWalletManageCubit extends Cubit<UsdtWalletManageState> {
  UsdtWalletManageCubit() : super(UsdtWalletManageState()) {
    fetchWalletList();
  }

  /// 获取USDT钱包地址列表
  Future<void> fetchWalletList() async {
    emit(state.copyWith(isLoading: true, error: null));

    try {
      final walletList = await WalletApi.fetchUsdtWalletAddressList();
      emit(state.copyWith(
        walletList: walletList,
        isLoading: false,
        hasLoadedOnce: true,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
        hasLoadedOnce: true,
      ));
    }
  }

  /// 删除钱包地址
  Future<void> deleteWallet(int id) async {
    emit(state.copyWith(isDeleting: true));

    try {
      final success = await WalletApi.deleteUsdtWalletAddress(id: id);
      if (success) {
        GPEasyLoading.showToast('deleteSuccess'.tr()); // 删除成功
        await fetchWalletList(); // 重新获取列表
      } else {
        GPEasyLoading.showToast('deleteFailed'.tr()); // 删除失败
      }
    } catch (e) {
      GPEasyLoading.showToast('deleteFailedWithError'.tr(args: [e.toString()])); // 删除失败: {}
    } finally {
      emit(state.copyWith(isDeleting: false));
    }
  }

  /// 设置默认钱包地址
  Future<void> setDefaultWallet(int id) async {
    emit(state.copyWith(isSettingDefault: true));

    try {
      // 找到要设置的钱包
      final wallet = state.walletList.firstWhere((w) => w.id == id);

      // 更新为默认地址
      final success = await WalletApi.editUsdtWalletAddress(
        id: id,
        walletAddress: wallet.walletAddress,
        network: wallet.network,
        isWithdrawDefault: true,
      );

      if (success) {
        GPEasyLoading.showToast('setSuccess'.tr()); // 设置成功
        await fetchWalletList(); // 重新获取列表
      } else {
        GPEasyLoading.showToast('setFailed'.tr()); // 设置失败
      }
    } catch (e) {
      GPEasyLoading.showToast('setFailedWithError'.tr(args: [e.toString()])); // 设置失败: {}
    } finally {
      emit(state.copyWith(isSettingDefault: false));
    }
  }


}
