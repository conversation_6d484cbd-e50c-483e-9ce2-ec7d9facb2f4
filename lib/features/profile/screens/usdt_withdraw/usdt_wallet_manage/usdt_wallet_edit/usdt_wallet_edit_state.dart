import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_wallet.dart';

class UsdtWalletEditState extends Equatable {
  final USDTWallet? wallet; // 如果为null则为新增，否则为编辑
  final String walletAddress;
  final int selectedNetwork;
  final bool isDefaultAddress;
  final bool isSubmitting;
  final String? error;
  final List<USDTNetworkType> networkList;

  const UsdtWalletEditState({
    this.wallet,
    this.walletAddress = '',
    this.selectedNetwork = 1, // 默认选择TRC20
    this.isDefaultAddress = false,
    this.isSubmitting = false,
    this.error,
    this.networkList = const [],
  });

  @override
  List<Object?> get props =>
      [wallet, walletAddress, selectedNetwork, isDefaultAddress, isSubmitting, error, networkList];

  UsdtWalletEditState copyWith({
    USDTWallet? wallet,
    String? walletAddress,
    int? selectedNetwork,
    bool? isDefaultAddress,
    bool? isSubmitting,
    String? error,
    List<USDTNetworkType>? networkList,
  }) {
    return UsdtWalletEditState(
      wallet: wallet ?? this.wallet,
      walletAddress: walletAddress ?? this.walletAddress,
      selectedNetwork: selectedNetwork ?? this.selectedNetwork,
      isDefaultAddress: isDefaultAddress ?? this.isDefaultAddress,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      error: error ?? this.error,
      networkList: networkList ?? this.networkList,
    );
  }
}
