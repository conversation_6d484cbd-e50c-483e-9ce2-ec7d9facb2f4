import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/entities/wallet/usdt_wallet.dart';

class UsdtWalletManageState extends Equatable {
  final List<USDTWallet> walletList;
  final bool isLoading;
  final String? error;
  final bool isDeleting;
  final bool isSettingDefault;
  final bool hasLoadedOnce;

  UsdtWalletManageState({
    this.walletList = const [],
    this.isLoading = false,
    this.error,
    this.isDeleting = false,
    this.isSettingDefault = false,
    this.hasLoadedOnce = false,
  });

  @override
  List<Object?> get props => [
        walletList,
        isLoading,
        error,
        isDeleting,
        isSettingDefault,
        hasLoadedOnce,
      ];

  UsdtWalletManageState copyWith({
    List<USDTWallet>? walletList,
    bool? isLoading,
    String? error,
    bool? isDeleting,
    bool? isSettingDefault,
    bool? hasLoadedOnce,
  }) {
    return UsdtWalletManageState(
      walletList: walletList ?? this.walletList,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isDeleting: isDeleting ?? this.isDeleting,
      isSettingDefault: isSettingDefault ?? this.isSettingDefault,
      hasLoadedOnce: hasLoadedOnce ?? this.hasLoadedOnce,
    );
  }
}
